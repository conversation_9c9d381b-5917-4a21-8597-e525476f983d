<?php
// Start session
session_start();

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $name = trim($_POST['name']);
    $email = trim($_POST['email']);
    $subject = trim($_POST['subject']);
    $message = trim($_POST['message']);
    
    // Insert message into database
    $sql = "INSERT INTO contact_messages (name, email, subject, message, created_at) 
            VALUES (?, ?, ?, ?, NOW())";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssss", $name, $email, $subject, $message);
    
    if ($stmt->execute()) {
        // Message sent successfully
        $_SESSION['success'] = "Your message has been sent successfully. We'll get back to you soon!";
        
        // Optional: Send email notification
        $to = "<EMAIL>";
        $subject = "New Contact Form Submission: $subject";
        $emailBody = "Name: $name\nEmail: $email\nSubject: $subject\nMessage: $message";
        $headers = "From: $email";
        
        mail($to, $subject, $emailBody, $headers);
        
        header("Location: contact.html");
        exit();
    } else {
        // Failed to send message
        $_SESSION['error'] = "Error: " . $stmt->error;
        header("Location: contact.html");
        exit();
    }
    
    $stmt->close();
}

$conn->close();
?>
