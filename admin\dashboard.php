<?php
session_start();
// Check if user is logged in and is admin
if(!isset($_SESSION['user_id']) || $_SESSION['user_role'] != 'admin') {
    header("Location: ../login.php");
    exit();
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get total users count
$usersQuery = "SELECT COUNT(*) as total FROM users WHERE role = 'user'";
$usersResult = $conn->query($usersQuery);
$totalUsers = $usersResult->fetch_assoc()['total'];

// Get today's attendance count
$todayAttendanceQuery = "SELECT COUNT(DISTINCT user_id) as total FROM attendance WHERE DATE(check_in) = CURDATE()";
$todayAttendanceResult = $conn->query($todayAttendanceQuery);
$todayAttendance = $todayAttendanceResult->fetch_assoc()['total'];

// Get total classes count
$classesQuery = "SELECT COUNT(*) as total FROM classes";
$classesResult = $conn->query($classesQuery);
$totalClasses = $classesResult->fetch_assoc()['total'];

// Get unread messages count
$messagesQuery = "SELECT COUNT(*) as total FROM contact_messages WHERE is_read = 0";
$messagesResult = $conn->query($messagesQuery);
$unreadMessages = $messagesResult->fetch_assoc()['total'];

// Get recent users
$recentUsersQuery = "SELECT * FROM users WHERE role = 'user' ORDER BY created_at DESC LIMIT 5";
$recentUsersResult = $conn->query($recentUsersQuery);

// Get today's attendance records
$todayRecordsQuery = "SELECT a.*, u.full_name 
                     FROM attendance a 
                     JOIN users u ON a.user_id = u.id 
                     WHERE DATE(a.check_in) = CURDATE() 
                     ORDER BY a.check_in DESC";
$todayRecordsResult = $conn->query($todayRecordsQuery);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - FitLife Gym</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <header class="admin-header">
        <div class="logo">
            <h2>FitLife Gym Admin</h2>
        </div>
        <nav>
            <ul>
                <li><a href="dashboard.php" class="active">Dashboard</a></li>
                <li><a href="users.php">Users</a></li>
                <li><a href="attendance.php">Attendance</a></li>
                <li><a href="classes.php">Classes</a></li>
                <li><a href="workouts.php">Workouts</a></li>
                <li><a href="messages.php">Messages <?php if($unreadMessages > 0): ?><span class="badge"><?php echo $unreadMessages; ?></span><?php endif; ?></a></li>
                <li><a href="../logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="admin-main">
        <h1>Admin Dashboard</h1>
        
        <section class="stats-cards">
            <div class="stat-card">
                <h3>Total Members</h3>
                <p class="stat-number"><?php echo $totalUsers; ?></p>
                <a href="users.php" class="btn-small">View All</a>
            </div>
            
            <div class="stat-card">
                <h3>Today's Attendance</h3>
                <p class="stat-number"><?php echo $todayAttendance; ?></p>
                <a href="attendance.php" class="btn-small">View Details</a>
            </div>
            
            <div class="stat-card">
                <h3>Total Classes</h3>
                <p class="stat-number"><?php echo $totalClasses; ?></p>
                <a href="classes.php" class="btn-small">Manage Classes</a>
            </div>
            
            <div class="stat-card">
                <h3>Unread Messages</h3>
                <p class="stat-number"><?php echo $unreadMessages; ?></p>
                <a href="messages.php" class="btn-small">View Messages</a>
            </div>
        </section>
        
        <section class="admin-content">
            <div class="admin-card">
                <h3>Recent Members</h3>
                <?php if ($recentUsersResult->num_rows > 0): ?>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Membership</th>
                            <th>Joined</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($user = $recentUsersResult->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $user['full_name']; ?></td>
                            <td><?php echo $user['email']; ?></td>
                            <td><?php echo ucfirst($user['membership_type']); ?></td>
                            <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                            <td>
                                <a href="edit_user.php?id=<?php echo $user['id']; ?>" class="btn-small">Edit</a>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <p>No members found.</p>
                <?php endif; ?>
                <a href="users.php" class="btn">View All Members</a>
            </div>
            
            <div class="admin-card">
                <h3>Today's Attendance</h3>
                <?php if ($todayRecordsResult->num_rows > 0): ?>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Member</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Duration</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($record = $todayRecordsResult->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $record['full_name']; ?></td>
                            <td><?php echo date('h:i A', strtotime($record['check_in'])); ?></td>
                            <td>
                                <?php 
                                if ($record['check_out']) {
                                    echo date('h:i A', strtotime($record['check_out']));
                                } else {
                                    echo '<span class="status-active">Active</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php 
                                if ($record['check_out']) {
                                    $checkIn = new DateTime($record['check_in']);
                                    $checkOut = new DateTime($record['check_out']);
                                    $duration = $checkOut->diff($checkIn);
                                    echo $duration->format('%h hrs %i mins');
                                } else {
                                    echo 'Still active';
                                }
                                ?>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <p>No attendance records for today.</p>
                <?php endif; ?>
                <a href="attendance.php" class="btn">View All Attendance</a>
            </div>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2023 FitLife Gym. All rights reserved.</p>
    </footer>
    <script src="../js/admin.js"></script>
</body>
</html>


