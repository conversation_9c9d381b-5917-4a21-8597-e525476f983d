<?php
// Database connection parameters
$servername = "localhost";
$username = "root";
$password = "";

// Create connection
$conn = new mysqli($servername, $username, $password);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create database
$sql = "CREATE DATABASE IF NOT EXISTS fitlife_gym";
if ($conn->query($sql) === TRUE) {
    echo "Database created successfully<br>";
} else {
    echo "Error creating database: " . $conn->error . "<br>";
}

// Select the database
$conn->select_db("fitlife_gym");

// Create users table
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20) NOT NULL,
    password VARCHAR(255) NOT NULL,
    membership_type VARCHAR(50) NOT NULL,
    role ENUM('user', 'admin') DEFAULT 'user',
    created_at DATETIME NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

if ($conn->query($sql) === TRUE) {
    echo "Users table created successfully<br>";
} else {
    echo "Error creating users table: " . $conn->error . "<br>";
}

// Create attendance table
$sql = "CREATE TABLE IF NOT EXISTS attendance (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    user_id INT(11) NOT NULL,
    check_in DATETIME NOT NULL,
    check_out DATETIME DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Attendance table created successfully<br>";
} else {
    echo "Error creating attendance table: " . $conn->error . "<br>";
}

// Create classes table
$sql = "CREATE TABLE IF NOT EXISTS classes (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    instructor VARCHAR(100) NOT NULL,
    day_of_week ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    max_capacity INT(11) NOT NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "Classes table created successfully<br>";
} else {
    echo "Error creating classes table: " . $conn->error . "<br>";
}

// Create class bookings table
$sql = "CREATE TABLE IF NOT EXISTS class_bookings (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    class_id INT(11) NOT NULL,
    user_id INT(11) NOT NULL,
    booking_date DATE NOT NULL,
    status ENUM('booked', 'attended', 'cancelled') DEFAULT 'booked',
    created_at DATETIME NOT NULL,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Class bookings table created successfully<br>";
} else {
    echo "Error creating class bookings table: " . $conn->error . "<br>";
}

// Create workout plans table
$sql = "CREATE TABLE IF NOT EXISTS workout_plans (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    difficulty_level ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
    created_at DATETIME NOT NULL
)";

if ($conn->query($sql) === TRUE) {
    echo "Workout plans table created successfully<br>";
} else {
    echo "Error creating workout plans table: " . $conn->error . "<br>";
}

// Create exercises table
$sql = "CREATE TABLE IF NOT EXISTS exercises (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    muscle_group VARCHAR(100) NOT NULL,
    equipment VARCHAR(100),
    instructions TEXT NOT NULL,
    image_url VARCHAR(255)
)";

if ($conn->query($sql) === TRUE) {
    echo "Exercises table created successfully<br>";
} else {
    echo "Error creating exercises table: " . $conn->error . "<br>";
}

// Create workout_exercises table (junction table)
$sql = "CREATE TABLE IF NOT EXISTS workout_exercises (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    workout_id INT(11) NOT NULL,
    exercise_id INT(11) NOT NULL,
    sets INT(11) NOT NULL,
    reps VARCHAR(50) NOT NULL,
    notes TEXT,
    FOREIGN KEY (workout_id) REFERENCES workout_plans(id) ON DELETE CASCADE,
    FOREIGN KEY (exercise_id) REFERENCES exercises(id) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Workout exercises table created successfully<br>";
} else {
    echo "Error creating workout exercises table: " . $conn->error . "<br>";
}

// Create contact_messages table
$sql = "CREATE TABLE IF NOT EXISTS contact_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    is_read TINYINT(1) DEFAULT 0
)";

if ($conn->query($sql) === TRUE) {
    echo "Contact messages table created successfully<br>";
} else {
    echo "Error creating contact messages table: " . $conn->error . "<br>";
}

// Insert admin user if not exists
$checkAdmin = "SELECT * FROM users WHERE email = '<EMAIL>'";
$result = $conn->query($checkAdmin);

if ($result->num_rows == 0) {
    // Admin doesn't exist, create one
    $adminName = "Admin User";
    $adminEmail = "<EMAIL>";
    $adminPhone = "1234567890";
    $adminPassword = password_hash("admin123", PASSWORD_DEFAULT);
    $adminRole = "admin";
    
    $sql = "INSERT INTO users (full_name, email, phone, password, membership_type, role, created_at) 
            VALUES ('$adminName', '$adminEmail', '$adminPhone', '$adminPassword', 'premium', '$adminRole', NOW())";
    
    if ($conn->query($sql) === TRUE) {
        echo "Admin user created successfully<br>";
    } else {
        echo "Error creating admin user: " . $conn->error . "<br>";
    }
}

// Insert sample classes
$checkClasses = "SELECT * FROM classes";
$result = $conn->query($checkClasses);

if ($result->num_rows == 0) {
    // No classes exist, add sample classes
    $classes = [
        ['Yoga', 'Relaxing yoga session for all levels', 'Sarah Johnson', 'Monday', '18:00:00', '19:00:00', 20],
        ['HIIT', 'High-intensity interval training', 'Mike Thompson', 'Wednesday', '19:00:00', '20:00:00', 15],
        ['Spinning', 'Indoor cycling workout', 'Jessica Adams', 'Friday', '17:30:00', '18:30:00', 12],
        ['Zumba', 'Dance fitness program', 'Maria Rodriguez', 'Tuesday', '18:30:00', '19:30:00', 25],
        ['Pilates', 'Core strengthening exercises', 'David Wilson', 'Thursday', '17:00:00', '18:00:00', 15]
    ];
    
    foreach ($classes as $class) {
        $sql = "INSERT INTO classes (name, description, instructor, day_of_week, start_time, end_time, max_capacity) 
                VALUES ('$class[0]', '$class[1]', '$class[2]', '$class[3]', '$class[4]', '$class[5]', $class[6])";
        
        if ($conn->query($sql) === TRUE) {
            echo "Class '$class[0]' added successfully<br>";
        } else {
            echo "Error adding class: " . $conn->error . "<br>";
        }
    }
}

// Insert sample exercises
$checkExercises = "SELECT * FROM exercises";
$result = $conn->query($checkExercises);

if ($result->num_rows == 0) {
    // No exercises exist, add sample exercises
    $exercises = [
        ['Push-ups', 'Basic upper body exercise', 'Chest', 'None', 'Start in a plank position with hands shoulder-width apart. Lower your body until your chest nearly touches the floor, then push back up.', 'pushup.jpg'],
        ['Squats', 'Lower body compound exercise', 'Legs', 'None', 'Stand with feet shoulder-width apart. Bend knees and lower hips as if sitting in a chair. Keep chest up and knees over toes.', 'squat.jpg'],
        ['Dumbbell Bicep Curls', 'Isolation exercise for biceps', 'Arms', 'Dumbbells', 'Hold dumbbells at your sides with palms facing forward. Curl the weights toward your shoulders while keeping elbows fixed.', 'bicep-curl.jpg'],
        ['Plank', 'Core stabilizing exercise', 'Core', 'None', 'Start in a push-up position but with weight on forearms. Keep body in straight line from head to heels.', 'plank.jpg'],
        ['Deadlift', 'Compound exercise for posterior chain', 'Back', 'Barbell', 'Stand with feet hip-width apart, barbell over midfoot. Bend at hips and knees to grip bar, then stand up by driving hips forward.', 'deadlift.jpg']
    ];
    
    foreach ($exercises as $exercise) {
        $sql = "INSERT INTO exercises (name, description, muscle_group, equipment, instructions, image_url) 
                VALUES ('$exercise[0]', '$exercise[1]', '$exercise[2]', '$exercise[3]', '$exercise[4]', '$exercise[5]')";
        
        if ($conn->query($sql) === TRUE) {
            echo "Exercise '$exercise[0]' added successfully<br>";
        } else {
            echo "Error adding exercise: " . $conn->error . "<br>";
        }
    }
}

// Insert sample workout plans
$checkWorkouts = "SELECT * FROM workout_plans";
$result = $conn->query($checkWorkouts);

if ($result->num_rows == 0) {
    // No workout plans exist, add sample plans
    $workouts = [
        ['Beginner Full Body', 'A simple full body workout for beginners', 'beginner'],
        ['Intermediate Split', 'Upper/lower body split for intermediate users', 'intermediate'],
        ['Advanced Hypertrophy', 'Muscle building program for advanced users', 'advanced']
    ];
    
    foreach ($workouts as $workout) {
        $sql = "INSERT INTO workout_plans (name, description, difficulty_level, created_at) 
                VALUES ('$workout[0]', '$workout[1]', '$workout[2]', NOW())";
        
        if ($conn->query($sql) === TRUE) {
            echo "Workout plan '$workout[0]' added successfully<br>";
            
            // Get the workout ID
            $workoutId = $conn->insert_id;
            
            // Add exercises to the workout plan
            if ($workout[2] == 'beginner') {
                $exercises = [
                    [1, 3, '10-12', 'Focus on form'],
                    [2, 3, '12-15', 'Keep back straight'],
                    [4, 3, '30 seconds', 'Maintain proper position']
                ];
            } elseif ($workout[2] == 'intermediate') {
                $exercises = [
                    [1, 4, '12-15', 'Control the movement'],
                    [2, 4, '15-20', 'Go deeper in the squat'],
                    [3, 3, '10-12', 'Use moderate weight'],
                    [4, 3, '45 seconds', 'Keep core tight']
                ];
            } else {
                $exercises = [
                    [1, 5, '8-10', 'Add weight if needed'],
                    [2, 5, '10-12', 'Use heavy weight'],
                    [3, 4, '8-10', 'Use heavy dumbbells'],
                    [5, 3, '5-8', 'Focus on proper form'],
                    [4, 3, '60 seconds', 'Advanced variation']
                ];
            }
            
            foreach ($exercises as $exercise) {
                $sql = "INSERT INTO workout_exercises (workout_id, exercise_id, sets, reps, notes) 
                        VALUES ($workoutId, $exercise[0], $exercise[1], '$exercise[2]', '$exercise[3]')";
                
                if ($conn->query($sql) === TRUE) {
                    echo "Exercise added to workout plan<br>";
                } else {
                    echo "Error adding exercise to workout plan: " . $conn->error . "<br>";
                }
            }
        } else {
            echo "Error adding workout plan: " . $conn->error . "<br>";
        }
    }
}

$conn->close();
echo "Database setup completed!";
?>

