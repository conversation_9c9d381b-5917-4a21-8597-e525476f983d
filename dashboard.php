<?php
session_start();
// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Check if user is admin, redirect to admin dashboard
if(isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') {
    header("Location: admin/dashboard.php");
    exit();
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user's attendance data
$userId = $_SESSION['user_id'];
$attendanceQuery = "SELECT * FROM attendance WHERE user_id = ? ORDER BY check_in DESC LIMIT 5";
$stmt = $conn->prepare($attendanceQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$attendanceResult = $stmt->get_result();

// Get upcoming classes
$classesQuery = "SELECT c.* FROM classes c 
                ORDER BY FIELD(c.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'), c.start_time
                LIMIT 5";
$stmt = $conn->prepare($classesQuery);
$stmt->execute();
$classesResult = $stmt->get_result();

// Get user's booked classes
$bookedClassesQuery = "SELECT c.*, cb.booking_date, cb.status 
                      FROM class_bookings cb 
                      JOIN classes c ON cb.class_id = c.id 
                      WHERE cb.user_id = ? AND cb.status = 'booked'
                      ORDER BY cb.booking_date, c.start_time";
$stmt = $conn->prepare($bookedClassesQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$bookedClassesResult = $stmt->get_result();

// Get user's workout plan based on membership type
$membershipType = $_SESSION['membership_type'];
$difficultyLevel = 'beginner'; // Default

if ($membershipType == 'standard') {
    $difficultyLevel = 'intermediate';
} elseif ($membershipType == 'premium') {
    $difficultyLevel = 'advanced';
}

$workoutQuery = "SELECT wp.*, we.sets, we.reps, we.notes, e.* 
                FROM workout_plans wp
                JOIN workout_exercises we ON wp.id = we.workout_id
                JOIN exercises e ON we.exercise_id = e.id
                WHERE wp.difficulty_level = ?
                ORDER BY wp.id, we.id";
$stmt = $conn->prepare($workoutQuery);
$stmt->bind_param("s", $difficultyLevel);
$stmt->execute();
$workoutResult = $stmt->get_result();

// Check if user is checked in
$checkedInQuery = "SELECT * FROM attendance WHERE user_id = ? AND check_out IS NULL ORDER BY check_in DESC LIMIT 1";
$stmt = $conn->prepare($checkedInQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$checkedInResult = $stmt->get_result();
$checkedIn = $checkedInResult->num_rows > 0;
$currentAttendance = $checkedIn ? $checkedInResult->fetch_assoc() : null;

// Process check-in/check-out
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['check_in']) && !$checkedIn) {
        // Process check-in
        $checkInQuery = "INSERT INTO attendance (user_id, check_in) VALUES (?, NOW())";
        $stmt = $conn->prepare($checkInQuery);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        header("Location: dashboard.php");
        exit();
    } elseif (isset($_POST['check_out']) && $checkedIn) {
        // Process check-out
        $attendanceId = $currentAttendance['id'];
        $checkOutQuery = "UPDATE attendance SET check_out = NOW() WHERE id = ?";
        $stmt = $conn->prepare($checkOutQuery);
        $stmt->bind_param("i", $attendanceId);
        $stmt->execute();
        header("Location: dashboard.php");
        exit();
    }
}

// Count total visits this month
$monthVisitsQuery = "SELECT COUNT(*) as total FROM attendance WHERE user_id = ? AND MONTH(check_in) = MONTH(CURRENT_DATE()) AND YEAR(check_in) = YEAR(CURRENT_DATE())";
$stmt = $conn->prepare($monthVisitsQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$monthVisitsResult = $stmt->get_result();
$monthVisits = $monthVisitsResult->fetch_assoc()['total'];

// Get last visit date (excluding current check-in if any)
$lastVisitQuery = "SELECT check_in FROM attendance WHERE user_id = ? AND check_out IS NOT NULL ORDER BY check_in DESC LIMIT 1";
$stmt = $conn->prepare($lastVisitQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$lastVisitResult = $stmt->get_result();
$lastVisit = $lastVisitResult->num_rows > 0 ? $lastVisitResult->fetch_assoc()['check_in'] : 'No previous visits';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - FitLife Gym</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <div class="logo">
            <h2>FitLife Gym</h2>
        </div>
        <nav>
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="about.php">About</a></li>
                <li><a href="facilities.php">Facilities</a></li>
                <li><a href="classes.php">Classes</a></li>
                <li><a href="workouts.php">Workouts</a></li>
                <li><a href="contact.php">Contact</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="dashboard-main">
        <section class="dashboard-header">
            <h1>Welcome, <?php echo $_SESSION['user_name']; ?></h1>
            <p>Your Membership: <span class="highlight"><?php echo ucfirst($_SESSION['membership_type']); ?></span></p>
            
            <div class="attendance-actions">
                <?php if (!$checkedIn): ?>
                <form method="post" action="">
                    <button type="submit" name="check_in" class="btn btn-success">Check In</button>
                </form>
                <?php else: ?>
                <p class="checked-in-status">You are currently checked in since <?php echo date('h:i A', strtotime($currentAttendance['check_in'])); ?></p>
                <form method="post" action="">
                    <button type="submit" name="check_out" class="btn btn-danger">Check Out</button>
                </form>
                <?php endif; ?>
            </div>
        </section>
        
        <section class="dashboard-content">
            <div class="dashboard-card">
                <h3>Your Stats</h3>
                <p><strong>Last Visit:</strong> <?php echo $lastVisit != 'No previous visits' ? date('F d, Y', strtotime($lastVisit)) : $lastVisit; ?></p>
                <p><strong>Total Visits This Month:</strong> <?php echo $monthVisits; ?></p>
                <p><strong>Membership Renewal:</strong> <?php echo date('F d, Y', strtotime('+1 month')); ?></p>
            </div>
            
            <div class="dashboard-card">
                <h3>Upcoming Classes</h3>
                <?php if ($classesResult->num_rows > 0): ?>
                <ul class="class-list">
                    <?php while($class = $classesResult->fetch_assoc()): ?>
                    <li>
                        <span class="class-day"><?php echo $class['day_of_week']; ?></span>
                        <span class="class-time"><?php echo date('h:i A', strtotime($class['start_time'])); ?> - <?php echo date('h:i A', strtotime($class['end_time'])); ?></span>
                        <span class="class-name"><?php echo $class['name']; ?></span>
                        <span class="class-instructor">with <?php echo $class['instructor']; ?></span>
                    </li>
                    <?php endwhile; ?>
                </ul>
                <a href="classes.php" class="btn btn-small">View All Classes</a>
                <?php else: ?>
                <p>No upcoming classes found.</p>
                <?php endif; ?>
            </div>
            
            <div class="dashboard-card">
                <h3>Your Booked Classes</h3>
                <?php if ($bookedClassesResult->num_rows > 0): ?>
                <ul class="booked-class-list">
                    <?php while($bookedClass = $bookedClassesResult->fetch_assoc()): ?>
                    <li>
                        <span class="class-date"><?php echo date('M d', strtotime($bookedClass['booking_date'])); ?></span>
                        <span class="class-day"><?php echo $bookedClass['day_of_week']; ?></span>
                        <span class="class-time"><?php echo date('h:i A', strtotime($bookedClass['start_time'])); ?></span>
                        <span class="class-name"><?php echo $bookedClass['name']; ?></span>
                        <a href="cancel_booking.php?id=<?php echo $bookedClass['id']; ?>" class="btn-cancel">Cancel</a>
                    </li>
                    <?php endwhile; ?>
                </ul>
                <?php else: ?>
                <p>You haven't booked any classes yet.</p>
                <a href="classes.php" class="btn btn-small">Book a Class</a>
                <?php endif; ?>
            </div>
            
            <div class="dashboard-card workout-preview">
                <h3>Your Recommended Workout</h3>
                <?php 
                $currentWorkoutId = 0;
                $workoutName = '';
                $workoutDescription = '';
                $exerciseCount = 0;
                
                if ($workoutResult->num_rows > 0):
                    $firstRow = $workoutResult->fetch_assoc();
                    $currentWorkoutId = $firstRow['id'];
                    $workoutName = $firstRow['name'];
                    $workoutDescription = $firstRow['description'];
                    
                    // Reset the pointer
                    $workoutResult->data_seek(0);
                ?>
                <div class="workout-info">
                    <h4><?php echo $workoutName; ?></h4>
                    <p><?php echo $workoutDescription; ?></p>
                </div>
                
                <div class="exercise-preview">
                    <?php 
                    while($exercise = $workoutResult->fetch_assoc()):
                        if ($exerciseCount < 3): // Show only first 3 exercises
                            $exerciseCount++;
                    ?>
                    <div class="exercise-item">
                        <div class="exercise-details">
                            <h5><?php echo $exercise['name']; ?></h5>
                            <p><?php echo $exercise['sets']; ?> sets x <?php echo $exercise['reps']; ?></p>
                        </div>
                    </div>
                    <?php 
                        endif;
                    endwhile; 
                    ?>
                </div>
                
                <a href="workouts.php?id=<?php echo $currentWorkoutId; ?>" class="btn btn-small">View Full Workout</a>
                <?php else: ?>
                <p>No workout plan available for your membership level.</p>
                <?php endif; ?>
            </div>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2023 FitLife Gym. All rights reserved.</p>
    </footer>
    <script src="js/dashboard.js"></script>
</body>
</html>

