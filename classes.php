<?php
session_start();
// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user ID
$userId = $_SESSION['user_id'];

// Get all classes with booking status for the user
$classesQuery = "SELECT c.*, 
                (SELECT COUNT(*) FROM class_bookings WHERE class_id = c.id AND status = 'booked') as current_bookings,
                (SELECT id FROM class_bookings WHERE class_id = c.id AND user_id = ? AND status = 'booked') as user_booking_id
                FROM classes c
                ORDER BY FIELD(c.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'), c.start_time";
$stmt = $conn->prepare($classesQuery);
$stmt->bind_param("i", $userId);
$stmt->execute();
$classesResult = $stmt->get_result();

// Process booking/cancellation
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['book_class'])) {
        $classId = $_POST['class_id'];
        $bookingDate = $_POST['booking_date'];
        
        // Check if class is full
        $checkCapacityQuery = "SELECT c.max_capacity, 
                              (SELECT COUNT(*) FROM class_bookings WHERE class_id = c.id AND booking_date = ? AND status = 'booked') as current_bookings
                              FROM classes c WHERE c.id = ?";
        $stmt = $conn->prepare($checkCapacityQuery);
        $stmt->bind_param("si", $bookingDate, $classId);
        $stmt->execute();
        $capacityResult = $stmt->get_result();
        $capacityData = $capacityResult->fetch_assoc();
        
        if ($capacityData['current_bookings'] < $capacityData['max_capacity']) {
            // Class has space, proceed with booking
            $bookQuery = "INSERT INTO class_bookings (class_id, user_id, booking_date, status, created_at) 
                         VALUES (?, ?, ?, 'booked', NOW())";
            $stmt = $conn->prepare($bookQuery);
            $stmt->bind_param("iis", $classId, $userId, $bookingDate);
            
            if ($stmt->execute()) {
                $_SESSION['success'] = "Class booked successfully!";
            } else {
                $_SESSION['error'] = "Error booking class: " . $stmt->error;
            }
        } else {
            $_SESSION['error'] = "Sorry, this class is full for the selected date.";
        }
        
        header("Location: classes.php");
        exit();
    } elseif (isset($_POST['cancel_booking'])) {
        $bookingId = $_POST['booking_id'];
        
        $cancelQuery = "DELETE FROM class_bookings WHERE id = ? AND user_id = ?";
        $stmt = $conn->prepare($cancelQuery);
        $stmt->bind_param("ii", $bookingId, $userId);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = "Booking cancelled successfully!";
        } else {
            $_SESSION['error'] = "Error cancelling booking: " . $stmt->error;
        }
        
        header("Location: classes.php");
        exit();
    }
}

// Get next 4 weeks of dates for booking dropdown
$dates = array();
$today = new DateTime();
for ($i = 0; $i < 28; $i++) {
    $date = clone $today;
    $date->modify("+$i days");
    $dates[] = $date;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Classes - FitLife Gym</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <div class="logo">
            <h2>FitLife Gym</h2>
        </div>
        <nav>
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="about.php">About</a></li>
                <li><a href="facilities.php">Facilities</a></li>
                <li><a href="classes.php" class="active">Classes</a></li>
                <li><a href="workouts.php">Workouts</a></li>
                <li><a href="contact.php">Contact</a></li>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="classes-main">
        <section class="page-header">
            <h1>Fitness Classes</h1>
            <p>Book your spot in our exciting group fitness classes</p>
        </section>
        
        <?php if(isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?php 
            echo $_SESSION['success']; 
            unset($_SESSION['success']);
            ?>
        </div>
        <?php endif; ?>
        
        <?php if(isset($_SESSION['error'])): ?>
        <div class="alert alert-error">
            <?php 
            echo $_SESSION['error']; 
            unset($_SESSION['error']);
            ?>
        </div>
        <?php endif; ?>
        
        <section class="classes-grid">
            <?php while($class = $classesResult->fetch_assoc()): ?>
            <div class="class-card">
                <div class="class-header">
                    <h3><?php echo $class['name']; ?></h3>
                    <span class="class-day"><?php echo $class['day_of_week']; ?></span>
                </div>
                
                <div class="class-details">
                    <p><strong>Time:</strong> <?php echo date('h:i A', strtotime($class['start_time'])); ?> - <?php echo date('h:i A', strtotime($class['end_time'])); ?></p>
                    <p><strong>Instructor:</strong> <?php echo $class['instructor']; ?></p>
                    <p><strong>Capacity:</strong> <?php echo $class['current_bookings']; ?>/<?php echo $class['max_capacity']; ?></p>
                    <p><?php echo $class['description']; ?></p>
                </div>
                
                <div class="class-actions">
                    <?php if($class['user_booking_id']): ?>
                    <form method="post" action="">
                        <input type="hidden" name="booking_id" value="<?php echo $class['user_booking_id']; ?>">
                        <button type="submit" name="cancel_booking" class="btn btn-danger">Cancel Booking</button>
                    </form>
                    <?php else: ?>
                    <form method="post" action="">
                        <input type="hidden" name="class_id" value="<?php echo $class['id']; ?>">
                        
                        <div class="form-group">
                            <label for="booking_date_<?php echo $class['id']; ?>">Select Date:</label>
                            <select id="booking_date_<?php echo $class['id']; ?>" name="booking_date" required>
                                <?php 
                                foreach($dates as $date) {
                                    // Only show dates that match the class day
                                    if($date->format('l') == $class['day_of_week']) {
                                        echo '<option value="' . $date->format('Y-m-d') . '">' . $date->format('M d, Y') . '</option>';
                                    }
                                }
                                ?>
                            </select>
                        </div>
                        
                        <button type="submit" name="book_class" class="btn">Book Class</button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
            <?php endwhile; ?>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2023 FitLife Gym. All rights reserved.</p>
    </footer>
</body>
</html>
