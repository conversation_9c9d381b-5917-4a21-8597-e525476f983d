document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('registrationForm');
    
    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Get form values
        const fullName = document.getElementById('fullName').value.trim();
        const email = document.getElementById('email').value.trim();
        const phone = document.getElementById('phone').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const membershipType = document.getElementById('membershipType').value;
        
        // Validate inputs
        let isValid = true;
        
        // Name validation
        if (fullName.length < 3) {
            showError('fullName', 'Name must be at least 3 characters');
            isValid = false;
        } else {
            removeError('fullName');
        }
        
        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            showError('email', 'Please enter a valid email address');
            isValid = false;
        } else {
            removeError('email');
        }
        
        // Phone validation
        const phoneRegex = /^\d{10}$/;
        if (!phoneRegex.test(phone)) {
            showError('phone', 'Please enter a valid 10-digit phone number');
            isValid = false;
        } else {
            removeError('phone');
        }
        
        // Password validation
        if (password.length < 6) {
            showError('password', 'Password must be at least 6 characters');
            isValid = false;
        } else {
            removeError('password');
        }
        
        // Confirm password
        if (password !== confirmPassword) {
            showError('confirmPassword', 'Passwords do not match');
            isValid = false;
        } else {
            removeError('confirmPassword');
        }
        
        // Membership type
        if (membershipType === '') {
            showError('membershipType', 'Please select a membership type');
            isValid = false;
        } else {
            removeError('membershipType');
        }
        
        // If valid, submit the form
        if (isValid) {
            // Convert to PHP form submission
            form.action = 'register_process.php';
            form.method = 'post';
            form.submit();
        }
    });
    
    // Helper functions
    function showError(fieldId, message) {
        const field = document.getElementById(fieldId);
        let errorElement = field.nextElementSibling;
        
        if (!errorElement || !errorElement.classList.contains('error-message')) {
            errorElement = document.createElement('div');
            errorElement.className = 'error-message';
            field.parentNode.insertBefore(errorElement, field.nextElementSibling);
        }
        
        errorElement.textContent = message;
        field.classList.add('error-input');
    }
    
    function removeError(fieldId) {
        const field = document.getElementById(fieldId);
        const errorElement = field.nextElementSibling;
        
        if (errorElement && errorElement.classList.contains('error-message')) {
            errorElement.remove();
        }
        
        field.classList.remove('error-input');
    }
});
