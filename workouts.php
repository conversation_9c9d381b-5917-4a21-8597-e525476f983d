<?php
session_start();
// Check if user is logged in
if(!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get user's membership type
$membershipType = $_SESSION['membership_type'];
$difficultyLevel = 'beginner'; // Default

if ($membershipType == 'standard') {
    $difficultyLevel = 'intermediate';
} elseif ($membershipType == 'premium') {
    $difficultyLevel = 'advanced';
}

// Get specific workout if ID is provided
$workoutId = isset($_GET['id']) ? $_GET['id'] : null;

if ($workoutId) {
    // Get specific workout details
    $workoutQuery = "SELECT * FROM workout_plans WHERE id = ?";
    $stmt = $conn->prepare($workoutQuery);
    $stmt->bind_param("i", $workoutId);
    $stmt->execute();
    $workoutResult = $stmt->get_result();
    $workout = $workoutResult->fetch_assoc();
    
    // Get exercises for this workout
    $exercisesQuery = "SELECT we.*, e.* 
                      FROM workout_exercises we
                      JOIN exercises e ON we.exercise_id = e.id
                      WHERE we.workout_id = ?
                      ORDER BY we.id";
    $stmt = $conn->prepare($exercisesQuery);
    $stmt->bind_param("i", $workoutId);
    $stmt->execute();
    $exercisesResult = $stmt->get_result();
} else {
    // Get all workouts for user's level
    $workoutsQuery = "SELECT * FROM workout_plans WHERE difficulty_level = ? ORDER BY id";
    $stmt = $conn->prepare($workoutsQuery);
    $stmt->bind_param("s", $difficultyLevel);
    $stmt->execute();
    $workoutsResult = $stmt->get_result();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workouts - FitLife Gym</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <header>
        <div class="logo">
            <h2>FitLife Gym</h2>
        </div>
        <nav>
            <ul>
                <li><a href="index.php">Home</a></li>
                <li><a href="about.php">About</a></li>
                <li><a href="facilities.php">Facilities</a></li>
                <li><a href="classes.php">Classes</a></li>
                <li><a href="workouts.php" class="active">Workouts</a></li>
                <li><a href="contact.php">Contact</a></li>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="workouts-main">
        <?php if ($workoutId && $workout): ?>
        <!-- Single Workout View -->
        <section class="workout-header">
            <h1><?php echo $workout['name']; ?></h1>
            <p class="workout-difficulty"><?php echo ucfirst($workout['difficulty_level']); ?> Level</p>
            <p class="workout-description"><?php echo $workout['description']; ?></p>
            <a href="workouts.php" class="btn btn-secondary">Back to All Workouts</a>
        </section>
        
        <section class="workout-exercises">
            <h2>Exercises</h2>
            <ul>
                <?php while ($exercise = $exercisesResult->fetch_assoc()): ?>
                <li>
                    <h3><?php echo $exercise['name']; ?></h3>
                    <p><?php echo $exercise['description']; ?></p>
                    <p><strong>Reps:</strong> <?php echo $exercise['reps']; ?></p>
                    <p><strong>Sets:</strong> <?php echo $exercise['sets']; ?></p>
                </li>
                <?php endwhile; ?>
            </ul>
        </section>
        <?php else: ?>
        <!-- Workout List View -->
        <section class="workout-list">
            <h2>Workouts</h2>
            <ul>
                <?php while ($workout = $workoutsResult->fetch_assoc()): ?>
                <li>
                    <a href="workouts.php?id=<?php echo $workout['id']; ?>">
                        <h3><?php echo $workout['name']; ?></h3>
                        <p class="workout-difficulty"><?php echo ucfirst($workout['difficulty_level']); ?> Level</p>
                    </a>
                </li>
                <?php endwhile; ?>
            </ul>
        </section>
        <?php endif; ?>
    </main>
</body>
</html>

