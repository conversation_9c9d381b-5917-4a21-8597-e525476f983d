<?php
// Start session
session_start();

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Process form data when form is submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $fullName = trim($_POST['fullName']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT); // Encrypt password
    $membershipType = $_POST['membershipType'];
    
    // Check if email already exists
    $checkEmail = "SELECT * FROM users WHERE email = ?";
    $stmt = $conn->prepare($checkEmail);
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        // Email already exists
        $_SESSION['error'] = "Email already registered. Please use a different email or login.";
        header("Location: registration.html");
        exit();
    }
    
    // Insert new user
    $sql = "INSERT INTO users (full_name, email, phone, password, membership_type, created_at) 
            VALUES (?, ?, ?, ?, ?, NOW())";
    
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssss", $fullName, $email, $phone, $password, $membershipType);
    
    if ($stmt->execute()) {
        // Registration successful
        $_SESSION['success'] = "Registration successful! You can now login.";
        header("Location: login.html");
        exit();
    } else {
        // Registration failed
        $_SESSION['error'] = "Error: " . $stmt->error;
        header("Location: registration.html");
        exit();
    }
    
    $stmt->close();
}

$conn->close();
?>
