<?php
session_start();
// Check if user is logged in and is admin
if(!isset($_SESSION['user_id']) || $_SESSION['user_role'] != 'admin') {
    header("Location: ../login.php");
    exit();
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Set default filter values
$dateFilter = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');
$userFilter = isset($_GET['user_id']) ? $_GET['user_id'] : '';

// Prepare the query based on filters
$query = "SELECT a.*, u.full_name, u.email 
          FROM attendance a 
          JOIN users u ON a.user_id = u.id 
          WHERE 1=1";

if (!empty($dateFilter)) {
    $query .= " AND DATE(a.check_in) = '$dateFilter'";
}

if (!empty($userFilter)) {
    $query .= " AND a.user_id = $userFilter";
}

$query .= " ORDER BY a.check_in DESC";

$result = $conn->query($query);

// Get all users for the filter dropdown
$usersQuery = "SELECT id, full_name FROM users WHERE role = 'user' ORDER BY full_name";
$usersResult = $conn->query($usersQuery);

// Process manual check-in/check-out if submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['manual_action'])) {
        $userId = $_POST['user_id'];
        $action = $_POST['action'];
        $dateTime = $_POST['date_time'];
        
        if ($action == 'check_in') {
            $sql = "INSERT INTO attendance (user_id, check_in) VALUES (?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("is", $userId, $dateTime);
            $stmt->execute();
        } else if ($action == 'check_out') {
            $attendanceId = $_POST['attendance_id'];
            $sql = "UPDATE attendance SET check_out = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("si", $dateTime, $attendanceId);
            $stmt->execute();
        }
        
        // Redirect to refresh the page
        header("Location: attendance.php?date=$dateFilter&user_id=$userFilter");
        exit();
    }
}

// Get users who are currently checked in
$activeUsersQuery = "SELECT a.id, a.user_id, a.check_in, u.full_name 
                    FROM attendance a 
                    JOIN users u ON a.user_id = u.id 
                    WHERE a.check_out IS NULL 
                    ORDER BY a.check_in";
$activeUsersResult = $conn->query($activeUsersQuery);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Management - FitLife Gym</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <header class="admin-header">
        <div class="logo">
            <h2>FitLife Gym Admin</h2>
        </div>
        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="users.php">Users</a></li>
                <li><a href="attendance.php" class="active">Attendance</a></li>
                <li><a href="classes.php">Classes</a></li>
                <li><a href="workouts.php">Workouts</a></li>
                <li><a href="messages.php">Messages</a></li>
                <li><a href="../logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="admin-main">
        <h1>Attendance Management</h1>
        
        <section class="admin-actions">
            <div class="admin-card">
                <h3>Manual Check-In/Out</h3>
                <form method="post" action="" class="admin-form">
                    <input type="hidden" name="manual_action" value="1">
                    
                    <div class="form-group">
                        <label for="user_id">Member:</label>
                        <select id="user_id" name="user_id" required>
                            <option value="">Select Member</option>
                            <?php while($user = $usersResult->fetch_assoc()): ?>
                            <option value="<?php echo $user['id']; ?>"><?php echo $user['full_name']; ?></option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="action">Action:</label>
                        <select id="action" name="action" required>
                            <option value="check_in">Check In</option>
                            <option value="check_out">Check Out</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="attendance_id_group" style="display: none;">
                        <label for="attendance_id">Active Session:</label>
                        <select id="attendance_id" name="attendance_id">
                            <option value="">Select Active Session</option>
                            <?php while($active = $activeUsersResult->fetch_assoc()): ?>
                            <option value="<?php echo $active['id']; ?>" data-user="<?php echo $active['user_id']; ?>">
                                <?php echo $active['full_name']; ?> (Since: <?php echo date('M d, h:i A', strtotime($active['check_in'])); ?>)
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="date_time">Date & Time:</label>
                        <input type="datetime-local" id="date_time" name="date_time" value="<?php echo date('Y-m-d\TH:i'); ?>" required>
                    </div>
                    
                    <button type="submit" class="btn">Submit</button>
                </form>
            </div>
            
            <div class="admin-card">
                <h3>Filter Attendance Records</h3>
                <form method="get" action="" class="admin-form">
                    <div class="form-group">
                        <label for="date">Date:</label>
                        <input type="date" id="date" name="date" value="<?php echo $dateFilter; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="filter_user">Member:</label>
                        <select id="filter_user" name="user_id">
                            <option value="">All Members</option>
                            <?php 
                            // Reset the pointer
                            $usersResult->data_seek(0);
                            while($user = $usersResult->fetch_assoc()): 
                            ?>
                            <option value="<?php echo $user['id']; ?>" <?php if($userFilter == $user['id']) echo 'selected'; ?>>
                                <?php echo $user['full_name']; ?>
                            </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn">Filter</button>
                    <a href="attendance.php" class="btn btn-secondary">Reset</a>
                </form>
            </div>
        </section>
        
        <section class="admin-content">
            <div class="admin-card full-width">
                <h3>Attendance Records</h3>
                <?php if ($result->num_rows > 0): ?>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Member</th>
                            <th>Email</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Duration</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($record = $result->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $record['id']; ?></td>
                            <td><?php echo $record['full_name']; ?></td>
                            <td><?php echo $record['email']; ?></td>
                            <td><?php echo date('M d, Y h:i A', strtotime($record['check_in'])); ?></td>
                            <td>
                                <?php 
                                if ($record['check_out']) {
                                    echo date('M d, Y h:i A', strtotime($record['check_out']));
                                } else {
                                    echo '<span class="status-active">Still Active</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <?php 
                                if ($record['check_out']) {
                                    $checkIn = new DateTime($record['check_in']);
                                    $checkOut = new DateTime($record['check_out']);
                                    $duration = $checkOut->diff($checkIn);
                                    echo $duration->format('%h hrs %i mins');
                                } else {
                                    $checkIn = new DateTime($record['check_in']);
                                    $now = new DateTime();
                                    $duration = $now->diff($checkIn);
                                    echo $duration->format('%h hrs %i mins (ongoing)');
                                }
                                ?>
                            </td>
                            <td>
                                <?php if (!$record['check_out']): ?>
                                <a href="manual_checkout.php?id=<?php echo $record['id']; ?>" class="btn-small">Check Out</a>
                                <?php endif; ?>
                                <a href="edit_attendance.php?id=<?php echo $record['id']; ?>" class="btn-small">Edit</a>
                                <a href="delete_attendance.php?id=<?php echo $record['id']; ?>" class="btn-small btn-danger" onclick="return confirm('Are you sure you want to delete this record?')">Delete</a>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <p>No attendance records found for the selected filters.</p>
                <?php endif; ?>
            </div>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2023 FitLife Gym. All rights reserved.</p>
    </footer>
    <script>
        // Script to show/hide attendance ID dropdown based on action selection
        document.getElementById('action').addEventListener('change', function() {
            const attendanceIdGroup = document.getElementById('attendance_id_group');
            if (this.value === 'check_out') {
                attendanceIdGroup.style.display = 'block';
            } else {
                attendanceIdGroup.style.display = 'none';
            }
        });
        
        // Script to auto-select user when attendance session is selected
        document.getElementById('attendance_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const userId = selectedOption.getAttribute('data-user');
            if (userId) {
                document.getElementById('user_id').value = userId;
            }
        });
    </script>
</body>
</html>
