<?php
session_start();
// Check if user is logged in and is admin
if(!isset($_SESSION['user_id']) || $_SESSION['user_role'] != 'admin') {
    header("Location: ../login.php");
    exit();
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "fitlife_gym";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Get all classes
$classesQuery = "SELECT c.*, 
                (SELECT COUNT(*) FROM class_bookings WHERE class_id = c.id AND status = 'booked') as total_bookings
                FROM classes c
                ORDER BY FIELD(c.day_of_week, 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'), c.start_time";
$classesResult = $conn->query($classesQuery);

// Process form submission for adding/editing class
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['add_class']) || isset($_POST['edit_class'])) {
        $name = trim($_POST['name']);
        $description = trim($_POST['description']);
        $instructor = trim($_POST['instructor']);
        $dayOfWeek = $_POST['day_of_week'];
        $startTime = $_POST['start_time'];
        $endTime = $_POST['end_time'];
        $capacity = $_POST['capacity'];
        $location = trim($_POST['location']);
        $price = $_POST['price'];
        $classId = isset($_POST['class_id']) ? $_POST['class_id'] : null;

        // Validate input
        if (empty($name) || empty($description) || empty($instructor) || empty($dayOfWeek) || empty($startTime) || empty($endTime) || empty($capacity) || empty($location) || empty($price)) {
            $_SESSION['error'] = "All fields are required.";
            header("Location: classes.php");
            exit();
        }

        // Check if class already exists
        if (!$classId) {
            $checkQuery = "SELECT * FROM classes WHERE name = ? AND day_of_week = ? AND start_time = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("sss", $name, $dayOfWeek, $startTime);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $_SESSION['error'] = "Class already exists.";
                header("Location: classes.php");
                exit();
            }
        }

        // Insert or update class
        if ($classId) {
            $updateQuery = "UPDATE classes SET name = ?, description = ?, instructor = ?, day_of_week = ?, start_time = ?, end_time = ?, max_capacity = ?, location = ?, price = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("ssssssisdi", $name, $description, $instructor, $dayOfWeek, $startTime, $endTime, $capacity, $location, $price, $classId);
        } else {
            $insertQuery = "INSERT INTO classes (name, description, instructor, day_of_week, start_time, end_time, max_capacity, location, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("ssssssisd", $name, $description, $instructor, $dayOfWeek, $startTime, $endTime, $capacity, $location, $price);
        }

        if ($stmt->execute()) {
            $_SESSION['success'] = "Class saved successfully.";
        } else {
            $_SESSION['error'] = "Error: " . $stmt->error;
        }
        
        header("Location: classes.php");
        exit();
    } elseif (isset($_POST['delete_class'])) {
        $classId = $_POST['class_id'];
        
        // Check if there are any bookings for this class
        $checkBookingsQuery = "SELECT COUNT(*) as total FROM class_bookings WHERE class_id = ?";
        $stmt = $conn->prepare($checkBookingsQuery);
        $stmt->bind_param("i", $classId);
        $stmt->execute();
        $result = $stmt->get_result();
        $bookingsCount = $result->fetch_assoc()['total'];
        
        if ($bookingsCount > 0) {
            // Delete all bookings first
            $deleteBookingsQuery = "DELETE FROM class_bookings WHERE class_id = ?";
            $stmt = $conn->prepare($deleteBookingsQuery);
            $stmt->bind_param("i", $classId);
            $stmt->execute();
        }
        
        // Now delete the class
        $deleteClassQuery = "DELETE FROM classes WHERE id = ?";
        $stmt = $conn->prepare($deleteClassQuery);
        $stmt->bind_param("i", $classId);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = "Class deleted successfully.";
        } else {
            $_SESSION['error'] = "Error deleting class: " . $stmt->error;
        }
        
        header("Location: classes.php");
        exit();
    }
}

// Get class details for editing
$editClass = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $classId = $_GET['edit'];
    $editQuery = "SELECT * FROM classes WHERE id = ?";
    $stmt = $conn->prepare($editQuery);
    $stmt->bind_param("i", $classId);
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $editClass = $result->fetch_assoc();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Classes - FitLife Gym</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
</head>
<body>
    <header class="admin-header">
        <div class="logo">
            <h2>FitLife Gym Admin</h2>
        </div>
        <nav>
            <ul>
                <li><a href="dashboard.php">Dashboard</a></li>
                <li><a href="users.php">Users</a></li>
                <li><a href="attendance.php">Attendance</a></li>
                <li><a href="classes.php" class="active">Classes</a></li>
                <li><a href="workouts.php">Workouts</a></li>
                <li><a href="messages.php">Messages</a></li>
                <li><a href="../logout.php">Logout</a></li>
            </ul>
        </nav>
    </header>
    
    <main class="admin-main">
        <h1>Manage Classes</h1>
        
        <?php if(isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?php 
            echo $_SESSION['success']; 
            unset($_SESSION['success']);
            ?>
        </div>
        <?php endif; ?>
        
        <?php if(isset($_SESSION['error'])): ?>
        <div class="alert alert-error">
            <?php 
            echo $_SESSION['error']; 
            unset($_SESSION['error']);
            ?>
        </div>
        <?php endif; ?>
        
        <section class="admin-content">
            <div class="admin-card">
                <h3><?php echo $editClass ? 'Edit Class' : 'Add New Class'; ?></h3>
                <form method="post" action="" class="admin-form">
                    <?php if($editClass): ?>
                    <input type="hidden" name="class_id" value="<?php echo $editClass['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="name">Class Name:</label>
                        <input type="text" id="name" name="name" value="<?php echo $editClass ? $editClass['name'] : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">Description:</label>
                        <textarea id="description" name="description" rows="3" required><?php echo $editClass ? $editClass['description'] : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="instructor">Instructor:</label>
                        <input type="text" id="instructor" name="instructor" value="<?php echo $editClass ? $editClass['instructor'] : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="day_of_week">Day of Week:</label>
                        <select id="day_of_week" name="day_of_week" required>
                            <option value="">Select Day</option>
                            <?php 
                            $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                            foreach($days as $day): 
                            ?>
                            <option value="<?php echo $day; ?>" <?php if($editClass && $editClass['day_of_week'] == $day) echo 'selected'; ?>><?php echo $day; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="start_time">Start Time:</label>
                            <input type="time" id="start_time" name="start_time" value="<?php echo $editClass ? $editClass['start_time'] : ''; ?>" required>
                        </div>
                        
                        <div class="form-group half">
                            <label for="end_time">End Time:</label>
                            <input type="time" id="end_time" name="end_time" value="<?php echo $editClass ? $editClass['end_time'] : ''; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group half">
                            <label for="capacity">Max Capacity:</label>
                            <input type="number" id="capacity" name="capacity" min="1" value="<?php echo $editClass ? $editClass['max_capacity'] : '20'; ?>" required>
                        </div>
                        
                        <div class="form-group half">
                            <label for="price">Price ($):</label>
                            <input type="number" id="price" name="price" min="0" step="0.01" value="<?php echo $editClass ? $editClass['price'] : '0.00'; ?>" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="location">Location:</label>
                        <input type="text" id="location" name="location" value="<?php echo $editClass ? $editClass['location'] : 'Main Studio'; ?>" required>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" name="<?php echo $editClass ? 'edit_class' : 'add_class'; ?>" class="btn"><?php echo $editClass ? 'Update Class' : 'Add Class'; ?></button>
                        <?php if($editClass): ?>
                        <a href="classes.php" class="btn btn-secondary">Cancel</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
            
            <div class="admin-card full-width">
                <h3>All Classes</h3>
                <?php if ($classesResult->num_rows > 0): ?>
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Day</th>
                            <th>Time</th>
                            <th>Instructor</th>
                            <th>Location</th>
                            <th>Capacity</th>
                            <th>Bookings</th>
                            <th>Price</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while($class = $classesResult->fetch_assoc()): ?>
                        <tr>
                            <td><?php echo $class['id']; ?></td>
                            <td><?php echo $class['name']; ?></td>
                            <td><?php echo $class['day_of_week']; ?></td>
                            <td><?php echo date('h:i A', strtotime($class['start_time'])); ?> - <?php echo date('h:i A', strtotime($class['end_time'])); ?></td>
                            <td><?php echo $class['instructor']; ?></td>
                            <td><?php echo $class['location']; ?></td>
                            <td><?php echo $class['max_capacity']; ?></td>
                            <td><?php echo $class['total_bookings']; ?></td>
                            <td>$<?php echo number_format($class['price'], 2); ?></td>
                            <td>
                                <a href="classes.php?edit=<?php echo $class['id']; ?>" class="btn-small">Edit</a>
                                <a href="view_bookings.php?class_id=<?php echo $class['id']; ?>" class="btn-small">View Bookings</a>
                                <form method="post" action="" style="display:inline;">
                                    <input type="hidden" name="class_id" value="<?php echo $class['id']; ?>">
                                    <button type="submit" name="delete_class" class="btn-small btn-danger" onclick="return confirm('Are you sure you want to delete this class? All bookings will also be deleted.')">Delete</button>
                                </form>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
                <?php else: ?>
                <p>No classes found.</p>
                <?php endif; ?>
            </div>
        </section>
    </main>
    
    <footer>
        <p>&copy; 2023 FitLife Gym. All rights reserved.</p>
    </footer>
</body>
</html>

