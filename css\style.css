/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

a {
    text-decoration: none;
    color: #333;
}

/* Header styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    background-color: #333;
    color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.logo h2 {
    font-size: 1.8rem;
    color: #fff;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li a {
    color: white;
    padding: 0.5rem 1rem;
    margin: 0 0.2rem;
    border-radius: 3px;
    transition: background-color 0.3s;
}

nav ul li a:hover {
    background-color: #555;
}

/* Button styles */
.btn {
    display: inline-block;
    background-color: #e74c3c;
    color: white;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #c0392b;
}

/* Hero section */
.hero {
    text-align: center;
    padding: 5rem 2rem;
    background-image: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('../images/gym-hero.jpg');
    background-size: cover;
    background-position: center;
    color: white;
}

.hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Features section */
.features {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    padding: 4rem 2rem;
    background-color: #fff;
}

.feature {
    flex: 1;
    min-width: 300px;
    text-align: center;
    padding: 2rem;
    margin: 1rem;
    border-radius: 5px;
    box
/* Dashboard styles */
.dashboard-main {
    padding: 2rem 5%;
}

.dashboard-header {
    background-color: #333;
    color: white;
    padding: 2rem;
    border-radius: 5px;
    margin-bottom: 2rem;
}

.dashboard-header h1 {
    margin-bottom: 0.5rem;
}

.dashboard-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background-color: white;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.dashboard-card h3 {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.dashboard-card ul {
    list-style: none;
}

.dashboard-card ul li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #f4f4f4;
}

.dashboard-card ul li:last-child {
    border-bottom: none;
}

.dashboard-card ul li a {
    color: #e74c3c;
}

.dashboard-card ul li a:hover {
    text-decoration: underline;
}

/* Message styles */
.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid #f5c6cb;
    border-radius: 0.25rem;
}

.success-message {
    background-color: #d4edda;
    color: #155724;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid #c3e6cb;
    border-radius: 0.25rem;
}

#message-container {
    margin-bottom: 1.5rem;
}

/* Form error styles */
.error-input {
    border: 1px solid #dc3545 !important;
}

.form-group .error-message {
    font-size: 0.8rem;
    padding: 0.3rem 0;
    margin-top: 0.2rem;
}

